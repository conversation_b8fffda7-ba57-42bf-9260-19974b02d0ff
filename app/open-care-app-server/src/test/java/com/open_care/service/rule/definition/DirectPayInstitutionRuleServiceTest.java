package com.open_care.service.rule.definition;

import com.open_care.dto.huagui.rule.*;
import com.open_care.enums.huagui.PriorityStrategyEnum;
import com.open_care.enums.huagui.RuleTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 直付机构规则服务条件验证测试
 */
@SpringBootTest
@Slf4j
public class DirectPayInstitutionRuleServiceTest {
    
    private final DirectPayInstitutionRuleService service = new DirectPayInstitutionRuleService();
    
    @Test
    public void testValidationGroupsForFixedPriority() {
        // 准备固定优先级策略的DTO
        RuleDefinitionDTO dto = createBaseRuleDTO();
        DirectPayInstitutionRuleConfigDTO config = DirectPayInstitutionRuleConfigDTO.builder()
            .strategy(PriorityStrategyEnum.FIXED_PRIORITY)
            .fixedPriorityConfig(FixedPriorityConfigDTO.builder()
                .platformPriorities(Arrays.asList(
                    PaymentPlatformPriorityDTO.builder()
                        .platformName("测试平台")
                        .priority(1)
                        .build()
                ))
                .build())
            .build();
        dto.setDirectPayInstitutionConfig(config);
        
        // 获取验证分组
        Class<?>[] groups = service.getValidationGroups(dto);

        // 验证使用继承分组：现在只返回DirectPayInstitutionFixedPriorityGroup，
        // 但由于继承关系，DirectPayInstitutionGroup和SaveRuleGroup的验证也会被执行
        assertEquals(1, groups.length);
        assertTrue(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionFixedPriorityGroup.class));
        assertFalse(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionDynamicVolumeGroup.class));

        log.info("固定优先级策略验证分组: {}", Arrays.toString(groups));
    }
    
    @Test
    public void testValidationGroupsForDynamicVolume() {
        // 准备动态分配策略的DTO
        RuleDefinitionDTO dto = createBaseRuleDTO();
        DirectPayInstitutionRuleConfigDTO config = DirectPayInstitutionRuleConfigDTO.builder()
            .strategy(PriorityStrategyEnum.DYNAMIC_VOLUME)
            .dynamicVolumeConfig(DynamicVolumeConfigDTO.builder()
                .platformConfigs(Collections.emptyList())
                .build())
            .build();
        dto.setDirectPayInstitutionConfig(config);
        
        // 获取验证分组
        Class<?>[] groups = service.getValidationGroups(dto);

        // 验证使用继承分组：现在只返回DirectPayInstitutionDynamicVolumeGroup，
        // 但由于继承关系，DirectPayInstitutionGroup和SaveRuleGroup的验证也会被执行
        assertEquals(1, groups.length);
        assertTrue(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionDynamicVolumeGroup.class));
        assertFalse(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionFixedPriorityGroup.class));

        log.info("动态分配策略验证分组: {}", Arrays.toString(groups));
    }
    
    @Test
    public void testValidationGroupsForNullStrategy() {
        // 准备没有策略的DTO
        RuleDefinitionDTO dto = createBaseRuleDTO();
        dto.setDirectPayInstitutionConfig(DirectPayInstitutionRuleConfigDTO.builder().build());

        // 获取验证分组
        Class<?>[] groups = service.getValidationGroups(dto);

        // 验证使用继承分组：现在只返回DirectPayInstitutionGroup，
        // 但由于继承关系，SaveRuleGroup的验证也会被执行
        assertEquals(1, groups.length);
        assertTrue(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionGroup.class));
        assertFalse(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionFixedPriorityGroup.class));
        assertFalse(Arrays.asList(groups).contains(RuleDefinitionDTO.DirectPayInstitutionDynamicVolumeGroup.class));

        log.info("空策略验证分组: {}", Arrays.toString(groups));
    }
    
    private RuleDefinitionDTO createBaseRuleDTO() {
        return RuleDefinitionDTO.builder()
            .ruleName("测试规则")
            .ruleType(RuleTypeEnum.DIRECT_PAY_INSTITUTION)
            .build();
    }
} 