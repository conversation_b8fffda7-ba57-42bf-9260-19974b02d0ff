package com.open_care.service.rule.definition;

import com.open_care.api.common.dto.OcResponse;
import com.open_care.dto.huagui.rule.RuleDefinitionDTO;
import com.open_care.enums.huagui.RuleTypeEnum;

/**
 * 规则服务统一接口
 * 定义所有规则服务的标准操作
 */
public interface RuleService {
    
    /**
     * 获取支持的规则类型
     * @return 规则类型枚举
     */
    RuleTypeEnum getSupportedRuleType();
    
    /**
     * 保存规则配置
     * @param dto 规则数据传输对象
     * @return 操作响应
     */
    OcResponse save(RuleDefinitionDTO dto);
    
    /**
     * 保存规则配置（带字段路径）
     * @param dto 规则数据传输对象
     * @param saveFieldPaths 需要保存的字段路径
     * @return 操作响应
     */
    OcResponse save(RuleDefinitionDTO dto, String[] saveFieldPaths);


    /**
     * 获取保存字段路径配置
     * @return 字段路径数组
     */
    String[] getSaveFieldPaths();
} 