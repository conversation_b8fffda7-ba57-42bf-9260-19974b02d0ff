package com.open_care.dto.huagui.rule;
import com.open_care.annotation.OcColumn;
import com.open_care.dto.IBaseDTO;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import com.open_care.validation.group.RuleValidationGroups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 支付平台优先级DTO
 * 所有属性都必须有值并且必填
 * 
 * <AUTHOR>
 * @date 2025-01-01
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
public class PaymentPlatformPriorityDTO implements IBaseDTO {
    @OcColumn(title = "平台ID")
    @NotBlank(message = "平台ID不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    private String platformId;
    @OcColumn(title = "平台名称")
    @NotBlank(message = "平台名称不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    @Size(max = 32, message = "平台名称长度不能超过32个字符", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    private String platformName;
    @OcColumn(title = "优先级")
    @NotNull(message = "优先级不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    @Positive(message = "优先级必须为大于0的正整数", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    private Integer priority;
    @OcColumn(title = "是否启用")
    @NotNull(message = "是否启用不能为空", groups = {
        RuleValidationGroups.DirectPayInstitutionFixedPriorityGroup.class
    })
    @Builder.Default
    private Boolean enabled = true;
} 