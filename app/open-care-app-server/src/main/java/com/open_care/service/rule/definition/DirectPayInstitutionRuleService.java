package com.open_care.service.rule.definition;

import com.open_care.crud_service.huagui.DirectPayInstitutionRuleCrudService;
import com.open_care.dto.huagui.rule.RuleDefinitionDTO;
import com.open_care.enums.huagui.PriorityStrategyEnum;
import com.open_care.enums.huagui.RuleTypeEnum;
import com.open_care.huagui.rule.OCDirectPayInstitutionRule;
import com.open_care.utils.FieldPathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 直付机构规则业务服务
 * 专注于直付机构规则的特定业务逻辑
 */
@Service
@Slf4j
public class DirectPayInstitutionRuleService extends AbstractRuleService<OCDirectPayInstitutionRule> {
    
    /**
     * 直付机构规则保存字段路径常量
     * 包含所有需要保存的实体属性，展开所有嵌套对象字段
     */
    public static final String[] DIRECT_PAY_INSTITUTION_SAVE_FIELD_PATHS = {
        // 基础字段（继承自OCBase）
        "ocId",
        "active", 
        "created",
        "createdBy",
        "createdByName",
        "updated", 
        "updatedBy",
        "updatedByName",
        "attributes",
        "dynamicFieldData",
        "version",
        "entityName",
        // 规则基本信息（继承自OCRuleDefinitionBase）
        "ruleName",
        "ruleDescription", 
        "ruleType",
        "effectiveTime",
        "expiryTime",
        // 直付机构特有字段
        "strategy",
        // 固定优先级配置对象及其子字段
        "fixedPriorityConfig",
        "fixedPriorityConfig.platformPriorities",
        "fixedPriorityConfig.platformPriorities.platformName",
        "fixedPriorityConfig.platformPriorities.priority",
        "fixedPriorityConfig.platformPriorities.enabled",
        // 动态单量配置对象及其子字段
        "dynamicVolumeConfig",
        "dynamicVolumeConfig.allocationStrategy",
        "dynamicVolumeConfig.statisticsDimension", 
        "dynamicVolumeConfig.statisticsPeriod",
        "dynamicVolumeConfig.rebalanceThreshold",
        "dynamicVolumeConfig.platformConfigs",
        "dynamicVolumeConfig.platformConfigs.platformName",
        "dynamicVolumeConfig.platformConfigs.targetRatio",
        "dynamicVolumeConfig.platformConfigs.minimumRatio"
    };
    
    @Autowired
    private DirectPayInstitutionRuleCrudService directPayInstitutionRuleCrudService;
    
    @Override
    public RuleTypeEnum getSupportedRuleType() {
        return RuleTypeEnum.DIRECT_PAY_INSTITUTION;
    }
    
    /**
     * 根据DTO内容动态获取验证分组
     * 根据分配策略选择不同的验证分组
     * 使用继承分组简化验证分组管理
     */
    @Override
    protected Class<?>[] getValidationGroups(RuleDefinitionDTO dto) {
        // 根据分配策略选择对应的继承验证分组
        if (dto.getDirectPayInstitutionConfig() != null &&
            dto.getDirectPayInstitutionConfig().getStrategy() != null) {

            PriorityStrategyEnum strategy = dto.getDirectPayInstitutionConfig().getStrategy();

            switch (strategy) {
                case FIXED_PRIORITY:
                    log.debug("使用固定优先级验证分组（自动包含基础分组）");
                    // DirectPayInstitutionFixedPriorityGroup继承DirectPayInstitutionGroup，
                    // DirectPayInstitutionGroup继承SaveRuleGroup，自动包含所有必要的验证
                    return new Class<?>[] {
                        RuleDefinitionDTO.DirectPayInstitutionFixedPriorityGroup.class
                    };
                case DYNAMIC_VOLUME:
                    log.debug("使用动态分配验证分组（自动包含基础分组）");
                    // DirectPayInstitutionDynamicVolumeGroup继承DirectPayInstitutionGroup，
                    // DirectPayInstitutionGroup继承SaveRuleGroup，自动包含所有必要的验证
                    return new Class<?>[] {
                        RuleDefinitionDTO.DirectPayInstitutionDynamicVolumeGroup.class
                    };
                default:
                    log.warn("未知的分配策略: {}，使用基础验证分组", strategy);
                    return new Class<?>[] {
                        RuleDefinitionDTO.DirectPayInstitutionGroup.class
                    };
            }
        } else {
            log.warn("直付机构配置或策略为空，使用基础验证分组");
            // DirectPayInstitutionGroup继承SaveRuleGroup，自动包含基础保存验证
            return new Class<?>[] {
                RuleDefinitionDTO.DirectPayInstitutionGroup.class
            };
        }
    }
    
    @Override
    protected Class<OCDirectPayInstitutionRule> getEntityClass() {
        return OCDirectPayInstitutionRule.class;
    }
    
    @Override
    protected OCDirectPayInstitutionRule saveEntity(OCDirectPayInstitutionRule entity) {
        return directPayInstitutionRuleCrudService.save(entity);
    }
    
    @Override
    protected OCDirectPayInstitutionRule saveEntity(OCDirectPayInstitutionRule entity, String[] saveFieldPaths) {
        return directPayInstitutionRuleCrudService.save(entity, saveFieldPaths);
    }
    
    @Override
    protected String getBusinessName() {
        return "直付机构规则";
    }
    
    @Override
    public String[] getSaveFieldPaths() {
        return DIRECT_PAY_INSTITUTION_SAVE_FIELD_PATHS;
    }
} 